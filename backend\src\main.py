"""
CodeQuilter Backend - Main FastAPI Application

This is the entry point for the CodeQuilter backend server.
Currently implements basic health check and project endpoints.
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# TODO: REPLACE_MOCK - Import real modules when implemented
# from .api import projects, components, generation
# from .session_manager import SessionManager

app = FastAPI(
    title="CodeQuilter Engine",
    description="AI-native development environment backend",
    version="0.1.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
)

# Configure CORS for frontend development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint - basic health check"""
    return {
        "message": "CodeQuilter Engine is running",
        "version": "0.1.0",
        "status": "healthy"
    }


@app.get("/api/health")
async def health_check():
    """Health check endpoint for monitoring"""
    return {
        "status": "healthy",
        "service": "codequilter-engine",
        "version": "0.1.0"
    }


# TODO: REPLACE_MOCK - Add real API routes when modules are implemented
@app.get("/api/projects")
async def list_projects():
    """Mock endpoint for listing projects"""
    return {
        "projects": [],
        "message": "Mock endpoint - real implementation coming soon"
    }


if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
