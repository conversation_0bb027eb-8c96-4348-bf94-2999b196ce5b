"""
Session management for CodeQuilter projects.

Manages active ProjectState objects, handles persistence,
and provides session lifecycle management.
"""

from typing import Dict, Optional, List
from datetime import datetime, timedelta
import asyncio
import json
from pathlib import Path

from .state.project_state import ProjectState, ProjectStatus


class SessionManager:
    """
    Manages active CodeQuilter project sessions.
    
    Handles creation, retrieval, persistence, and cleanup of ProjectState objects.
    In production, this would integrate with a database or persistent storage.
    """
    
    def __init__(self, session_timeout_minutes: int = 60):
        self._active_sessions: Dict[str, ProjectState] = {}
        self._session_timeout = timedelta(minutes=session_timeout_minutes)
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # TODO: REPLACE_MOCK - Add real persistence layer
        self._storage_path = Path("./session_storage")
        self._storage_path.mkdir(exist_ok=True)
    
    async def start(self) -> None:
        """Start the session manager and cleanup task"""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def stop(self) -> None:
        """Stop the session manager and cleanup task"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
    
    def create_session(self, project_brief: str = "", project_name: str = "") -> ProjectState:
        """Create a new project session"""
        project_state = ProjectState()
        project_state.project_brief = project_brief
        project_state.project_name = project_name or f"project-{project_state.session_id[:8]}"
        
        self._active_sessions[project_state.session_id] = project_state
        
        # TODO: REPLACE_MOCK - Persist to real storage
        self._save_session_to_disk(project_state)
        
        return project_state
    
    def get_session(self, session_id: str) -> Optional[ProjectState]:
        """Retrieve an active session by ID"""
        session = self._active_sessions.get(session_id)
        
        if session:
            # Update last accessed time
            session.last_modified = datetime.now()
            return session
        
        # Try to load from disk if not in memory
        # TODO: REPLACE_MOCK - Load from real storage
        return self._load_session_from_disk(session_id)
    
    def update_session(self, session_id: str, project_state: ProjectState) -> bool:
        """Update an existing session"""
        if session_id not in self._active_sessions:
            return False
        
        project_state.last_modified = datetime.now()
        self._active_sessions[session_id] = project_state
        
        # TODO: REPLACE_MOCK - Persist to real storage
        self._save_session_to_disk(project_state)
        
        return True
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        if session_id in self._active_sessions:
            del self._active_sessions[session_id]
            
            # TODO: REPLACE_MOCK - Remove from real storage
            session_file = self._storage_path / f"{session_id}.json"
            if session_file.exists():
                session_file.unlink()
            
            return True
        return False
    
    def list_sessions(self, limit: int = 50) -> List[Dict[str, any]]:
        """List all active sessions with basic info"""
        sessions = []
        for session_id, project_state in self._active_sessions.items():
            sessions.append({
                "session_id": session_id,
                "project_name": project_state.project_name,
                "status": project_state.status.value,
                "created_at": project_state.created_at.isoformat(),
                "last_modified": project_state.last_modified.isoformat(),
                "progress": project_state.get_progress_percentage()
            })
        
        # Sort by last modified, most recent first
        sessions.sort(key=lambda x: x["last_modified"], reverse=True)
        return sessions[:limit]
    
    def get_session_count(self) -> int:
        """Get the number of active sessions"""
        return len(self._active_sessions)
    
    def cleanup_expired_sessions(self) -> int:
        """Manually cleanup expired sessions, returns count of cleaned sessions"""
        now = datetime.now()
        expired_sessions = []
        
        for session_id, project_state in self._active_sessions.items():
            if now - project_state.last_modified > self._session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        return len(expired_sessions)
    
    async def _cleanup_expired_sessions(self) -> None:
        """Background task to cleanup expired sessions"""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                cleaned = self.cleanup_expired_sessions()
                if cleaned > 0:
                    print(f"Cleaned up {cleaned} expired sessions")
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in session cleanup: {e}")
    
    def _save_session_to_disk(self, project_state: ProjectState) -> None:
        """Save session to disk (mock implementation)"""
        # TODO: REPLACE_MOCK - Replace with real database persistence
        try:
            session_file = self._storage_path / f"{project_state.session_id}.json"
            session_data = project_state.to_dict()
            
            with open(session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
        except Exception as e:
            print(f"Error saving session to disk: {e}")
    
    def _load_session_from_disk(self, session_id: str) -> Optional[ProjectState]:
        """Load session from disk (mock implementation)"""
        # TODO: REPLACE_MOCK - Replace with real database loading
        try:
            session_file = self._storage_path / f"{session_id}.json"
            if not session_file.exists():
                return None
            
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            project_state = ProjectState.from_dict(session_data)
            
            # Add to active sessions
            self._active_sessions[session_id] = project_state
            
            return project_state
        except Exception as e:
            print(f"Error loading session from disk: {e}")
            return None
    
    def get_stats(self) -> Dict[str, any]:
        """Get session manager statistics"""
        status_counts = {}
        for project_state in self._active_sessions.values():
            status = project_state.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_sessions": len(self._active_sessions),
            "status_breakdown": status_counts,
            "session_timeout_minutes": self._session_timeout.total_seconds() / 60,
            "storage_path": str(self._storage_path)
        }


# Global session manager instance
session_manager = SessionManager()


async def get_session_manager() -> SessionManager:
    """Dependency injection for FastAPI"""
    return session_manager
