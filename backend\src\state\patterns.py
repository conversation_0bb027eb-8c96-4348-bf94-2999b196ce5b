"""
Architectural pattern definitions for CodeQuilter.

This module defines the core architectural patterns that <PERSON><PERSON><PERSON><PERSON> uses
to guide component discovery and code generation.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional
from enum import Enum


class PatternCategory(Enum):
    """Categories of architectural patterns"""
    COMMUNICATION = "communication"
    INTEGRATION = "integration"
    STRUCTURE = "structure"
    BEHAVIOR = "behavior"


@dataclass
class SearchCriteria:
    """Criteria for finding components that implement a pattern"""
    github_keywords: List[str] = field(default_factory=list)
    sourcegraph_regex: List[str] = field(default_factory=list)
    file_patterns: List[str] = field(default_factory=list)
    language_specific: Dict[str, List[str]] = field(default_factory=dict)
    min_stars: int = 1000
    license_types: List[str] = field(default_factory=lambda: ["MIT", "Apache-2.0", "BSD-3-Clause"])


@dataclass
class TestStrategy:
    """Strategy for verifying pattern implementation"""
    test_commands: List[str] = field(default_factory=list)
    expected_files: List[str] = field(default_factory=list)
    integration_tests: List[str] = field(default_factory=list)
    performance_benchmarks: List[str] = field(default_factory=list)


@dataclass
class ArchitecturalPattern:
    """
    Represents a software architectural pattern that CodeQuilter can work with.
    
    Each pattern defines how to find components, generate adapters,
    and verify implementations.
    """
    
    # Core Identity
    name: str
    description: str
    category: PatternCategory
    
    # Discovery & Selection
    search_criteria: SearchCriteria
    
    # Code Generation
    adapter_templates: Dict[str, str] = field(default_factory=dict)
    common_interfaces: List[str] = field(default_factory=list)
    
    # Verification
    verification_strategy: TestStrategy = field(default_factory=TestStrategy)
    
    # Metadata
    complexity_level: str = "intermediate"  # beginner, intermediate, advanced
    typical_use_cases: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)  # Other patterns this depends on
    
    def get_search_query(self, language: str = "python") -> str:
        """Generate GitHub search query for this pattern"""
        base_keywords = " ".join(self.search_criteria.github_keywords)
        lang_keywords = " ".join(self.search_criteria.language_specific.get(language, []))
        
        query_parts = [
            f"language:{language}",
            f"stars:>{self.search_criteria.min_stars}",
            base_keywords,
            lang_keywords
        ]
        
        return " ".join(filter(None, query_parts))


# Define the core pattern palette
PATTERN_PALETTE: Dict[str, ArchitecturalPattern] = {
    
    "api_gateway": ArchitecturalPattern(
        name="API Gateway",
        description="Single entry point for all client requests, routing to appropriate services",
        category=PatternCategory.COMMUNICATION,
        search_criteria=SearchCriteria(
            github_keywords=["api", "gateway", "router", "proxy"],
            sourcegraph_regex=[
                r"class.*Gateway",
                r"@app\.route",
                r"router\.",
                r"FastAPI.*router"
            ],
            file_patterns=["*gateway*", "*router*", "*proxy*"],
            language_specific={
                "python": ["fastapi", "flask", "django", "starlette"],
                "javascript": ["express", "koa", "hapi", "nestjs"],
                "java": ["spring-boot", "jersey", "jax-rs"]
            }
        ),
        adapter_templates={
            "fastapi_to_service": """
# TODO: REPLACE_MOCK - Real template will be generated
async def create_gateway_route(service_endpoint: str, path: str):
    @app.{method}(path)
    async def gateway_handler(request: Request):
        # Forward request to service
        pass
""",
        },
        common_interfaces=["route", "middleware", "handler"],
        typical_use_cases=[
            "Microservices entry point",
            "API versioning and routing",
            "Authentication and rate limiting",
            "Request/response transformation"
        ],
        complexity_level="intermediate"
    ),
    
    "adapter": ArchitecturalPattern(
        name="Adapter",
        description="Wrapper that makes incompatible interfaces work together",
        category=PatternCategory.INTEGRATION,
        search_criteria=SearchCriteria(
            github_keywords=["adapter", "wrapper", "bridge", "connector"],
            sourcegraph_regex=[
                r"class.*Adapter",
                r"def adapt",
                r"interface.*Adapter",
                r"@adapter"
            ],
            file_patterns=["*adapter*", "*wrapper*", "*bridge*"],
            language_specific={
                "python": ["abc", "protocol", "typing"],
                "javascript": ["interface", "class", "prototype"],
                "java": ["interface", "implements", "adapter"]
            }
        ),
        adapter_templates={
            "class_to_class": """
# TODO: REPLACE_MOCK - Real template will be generated
class {SourceClass}To{TargetClass}Adapter:
    def __init__(self, source: {SourceClass}):
        self.source = source
    
    def {target_method}(self, *args, **kwargs):
        # Adapt source method to target interface
        return self.source.{source_method}(*args, **kwargs)
""",
        },
        common_interfaces=["adapt", "convert", "transform"],
        typical_use_cases=[
            "Legacy system integration",
            "Third-party library wrapping",
            "Interface standardization",
            "Data format conversion"
        ],
        complexity_level="beginner"
    ),
    
    "pub_sub": ArchitecturalPattern(
        name="Publish-Subscribe",
        description="Event-driven pattern where publishers send messages to topics, subscribers receive them",
        category=PatternCategory.COMMUNICATION,
        search_criteria=SearchCriteria(
            github_keywords=["pubsub", "publish", "subscribe", "event", "message"],
            sourcegraph_regex=[
                r"publish\(",
                r"subscribe\(",
                r"EventEmitter",
                r"@event",
                r"on\(.*event"
            ],
            file_patterns=["*pubsub*", "*event*", "*message*"],
            language_specific={
                "python": ["asyncio", "redis", "rabbitmq", "kafka"],
                "javascript": ["eventemitter", "socket.io", "mqtt"],
                "java": ["spring-events", "guava-eventbus"]
            }
        ),
        typical_use_cases=[
            "User action notifications",
            "System event broadcasting",
            "Microservice communication",
            "Real-time updates"
        ],
        complexity_level="intermediate"
    ),
    
    "message_queue": ArchitecturalPattern(
        name="Message Queue",
        description="Point-to-point asynchronous communication via message queues",
        category=PatternCategory.COMMUNICATION,
        search_criteria=SearchCriteria(
            github_keywords=["queue", "message", "worker", "task", "job"],
            sourcegraph_regex=[
                r"Queue\(",
                r"enqueue\(",
                r"dequeue\(",
                r"@task",
                r"worker\."
            ],
            file_patterns=["*queue*", "*worker*", "*task*"],
            language_specific={
                "python": ["celery", "rq", "dramatiq", "asyncio.Queue"],
                "javascript": ["bull", "agenda", "kue"],
                "java": ["spring-jms", "activemq", "rabbitmq"]
            }
        ),
        typical_use_cases=[
            "Background job processing",
            "Email sending",
            "File processing",
            "Long-running tasks"
        ],
        complexity_level="intermediate"
    ),
    
    "rest_api": ArchitecturalPattern(
        name="REST API",
        description="Synchronous HTTP-based request-response communication",
        category=PatternCategory.COMMUNICATION,
        search_criteria=SearchCriteria(
            github_keywords=["rest", "api", "http", "endpoint", "resource"],
            sourcegraph_regex=[
                r"@app\.(get|post|put|delete)",
                r"@RestController",
                r"app\.(get|post|put|delete)",
                r"router\.(get|post|put|delete)"
            ],
            file_patterns=["*api*", "*rest*", "*endpoint*"],
            language_specific={
                "python": ["fastapi", "flask", "django-rest", "starlette"],
                "javascript": ["express", "koa", "restify"],
                "java": ["spring-boot", "jersey", "jax-rs"]
            }
        ),
        typical_use_cases=[
            "CRUD operations",
            "Data retrieval",
            "Service integration",
            "Mobile app backends"
        ],
        complexity_level="beginner"
    )
}


def get_pattern(name: str) -> Optional[ArchitecturalPattern]:
    """Get a pattern by name"""
    return PATTERN_PALETTE.get(name)


def get_patterns_by_category(category: PatternCategory) -> List[ArchitecturalPattern]:
    """Get all patterns in a specific category"""
    return [pattern for pattern in PATTERN_PALETTE.values() if pattern.category == category]


def get_beginner_patterns() -> List[ArchitecturalPattern]:
    """Get patterns suitable for beginners"""
    return [pattern for pattern in PATTERN_PALETTE.values() if pattern.complexity_level == "beginner"]


def suggest_patterns_for_use_case(use_case: str) -> List[ArchitecturalPattern]:
    """Suggest patterns based on a use case description"""
    # TODO: REPLACE_MOCK - Implement intelligent pattern suggestion
    # For now, return patterns that mention the use case
    suggestions = []
    use_case_lower = use_case.lower()
    
    for pattern in PATTERN_PALETTE.values():
        for typical_case in pattern.typical_use_cases:
            if any(word in typical_case.lower() for word in use_case_lower.split()):
                suggestions.append(pattern)
                break
    
    return suggestions
