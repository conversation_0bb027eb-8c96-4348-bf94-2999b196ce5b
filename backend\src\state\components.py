"""
Data models for components, repositories, and generated code.

This module defines the structures for representing GitHub repositories,
generated adapter code, test results, and other component-related data.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid


class ComponentStatus(Enum):
    """Status of a component in the project"""
    DISCOVERED = "discovered"
    ANALYZING = "analyzing"
    SELECTED = "selected"
    INTEGRATING = "integrating"
    INTEGRATED = "integrated"
    FAILED = "failed"


class ComponentTestStatus(Enum):
    """Status of component testing"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"


class SecurityLevel(Enum):
    """Security assessment levels"""
    SAFE = "safe"
    LOW_RISK = "low_risk"
    MEDIUM_RISK = "medium_risk"
    HIGH_RISK = "high_risk"
    CRITICAL = "critical"


@dataclass
class GitHubRepo:
    """
    Represents a GitHub repository that could be used as a component.
    
    Contains all metadata needed for evaluation, selection, and integration.
    """
    
    # Basic Repository Info
    name: str
    full_name: str  # owner/repo
    description: str = ""
    url: str = ""
    clone_url: str = ""
    
    # Quality Metrics
    stars: int = 0
    forks: int = 0
    watchers: int = 0
    open_issues: int = 0
    closed_issues: int = 0
    
    # Activity Metrics
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    pushed_at: Optional[datetime] = None
    
    # Technical Details
    language: str = ""
    languages: Dict[str, int] = field(default_factory=dict)  # language -> bytes
    license: Optional[str] = None
    topics: List[str] = field(default_factory=list)
    
    # CodeQuilter Analysis
    pattern_match: str = ""  # Which pattern this repo implements
    confidence_score: float = 0.0  # 0-1 confidence in pattern match
    compatibility_score: float = 0.0  # 0-1 compatibility with other components
    status: ComponentStatus = ComponentStatus.DISCOVERED
    
    # Integration Details
    main_files: List[str] = field(default_factory=list)  # Key files identified
    api_surface: Dict[str, Any] = field(default_factory=dict)  # Extracted API info
    dependencies: List[str] = field(default_factory=list)  # Package dependencies
    
    def get_quality_score(self) -> float:
        """Calculate overall quality score (0-1)"""
        # Weighted scoring based on multiple factors
        star_score = min(self.stars / 10000, 1.0) * 0.3  # Max 10k stars
        activity_score = 0.2 if self.pushed_at and (datetime.now() - self.pushed_at).days < 365 else 0.0
        issue_score = 0.2 if self.open_issues < self.closed_issues else 0.1
        license_score = 0.3 if self.license in ["MIT", "Apache-2.0", "BSD-3-Clause"] else 0.0
        
        return star_score + activity_score + issue_score + license_score
    
    def is_recently_active(self, days: int = 365) -> bool:
        """Check if repo has been active recently"""
        if not self.pushed_at:
            return False
        return (datetime.now() - self.pushed_at).days <= days
    
    def get_primary_language(self) -> str:
        """Get the primary programming language"""
        if not self.languages:
            return self.language
        return max(self.languages.keys(), key=lambda k: self.languages[k])
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "name": self.name,
            "full_name": self.full_name,
            "description": self.description,
            "url": self.url,
            "stars": self.stars,
            "language": self.get_primary_language(),
            "license": self.license,
            "pattern_match": self.pattern_match,
            "confidence_score": self.confidence_score,
            "quality_score": self.get_quality_score(),
            "recently_active": self.is_recently_active(),
            "status": self.status.value
        }


@dataclass
class GeneratedCode:
    """
    Represents code generated by CodeQuilter for component integration.
    """
    
    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    
    # Generation Context
    source_component: str = ""  # Source component name
    target_component: str = ""  # Target component name
    pattern_type: str = ""      # Pattern being implemented
    
    # Generated Content
    code: str = ""              # The actual generated code
    file_path: str = ""         # Where this code should be placed
    language: str = "python"    # Programming language
    
    # Metadata
    description: str = ""       # Human-readable description
    dependencies: List[str] = field(default_factory=list)  # Required imports/packages
    test_code: str = ""         # Generated test code
    
    # Quality Metrics
    lines_of_code: int = 0
    complexity_score: float = 0.0
    test_coverage: float = 0.0
    
    def calculate_metrics(self) -> None:
        """Calculate code quality metrics"""
        self.lines_of_code = len([line for line in self.code.split('\n') if line.strip()])
        # TODO: REPLACE_MOCK - Implement real complexity analysis
        self.complexity_score = min(self.lines_of_code / 100, 1.0)  # Simple mock
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "source_component": self.source_component,
            "target_component": self.target_component,
            "pattern_type": self.pattern_type,
            "file_path": self.file_path,
            "language": self.language,
            "description": self.description,
            "lines_of_code": self.lines_of_code,
            "dependencies": self.dependencies
        }


@dataclass
class ComponentTestResult:
    """
    Results from testing a component or integration.
    """

    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)

    # Test Context
    component_name: str = ""
    test_type: str = ""  # "unit", "integration", "security", "performance"

    # Results
    status: ComponentTestStatus = ComponentTestStatus.PENDING
    passed: int = 0
    failed: int = 0
    skipped: int = 0
    duration_seconds: float = 0.0
    
    # Details
    output: str = ""            # Test output/logs
    error_message: str = ""     # Error details if failed
    coverage_percentage: float = 0.0
    
    # Files
    test_files: List[str] = field(default_factory=list)
    
    def get_success_rate(self) -> float:
        """Calculate test success rate"""
        total = self.passed + self.failed
        return (self.passed / total) if total > 0 else 0.0
    
    def is_passing(self) -> bool:
        """Check if tests are passing"""
        return self.status == ComponentTestStatus.PASSED and self.failed == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "component_name": self.component_name,
            "test_type": self.test_type,
            "status": self.status.value,
            "passed": self.passed,
            "failed": self.failed,
            "success_rate": self.get_success_rate(),
            "duration_seconds": self.duration_seconds,
            "coverage_percentage": self.coverage_percentage,
            "is_passing": self.is_passing()
        }


@dataclass
class SecurityReport:
    """
    Security analysis results for components and dependencies.
    """
    
    # Identity
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Scope
    component_name: str = ""
    dependencies_scanned: List[str] = field(default_factory=list)
    
    # Results
    security_level: SecurityLevel = SecurityLevel.SAFE
    vulnerabilities_found: int = 0
    critical_issues: int = 0
    high_issues: int = 0
    medium_issues: int = 0
    low_issues: int = 0
    
    # Details
    vulnerability_details: List[Dict[str, Any]] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    # License Analysis
    license_issues: List[str] = field(default_factory=list)
    license_compatibility: bool = True
    
    def get_risk_score(self) -> float:
        """Calculate overall risk score (0-1, higher is riskier)"""
        weights = {"critical": 1.0, "high": 0.7, "medium": 0.4, "low": 0.1}
        total_score = (
            self.critical_issues * weights["critical"] +
            self.high_issues * weights["high"] +
            self.medium_issues * weights["medium"] +
            self.low_issues * weights["low"]
        )
        # Normalize to 0-1 scale (assuming max 10 critical issues = 1.0 risk)
        return min(total_score / 10.0, 1.0)
    
    def is_safe_to_use(self) -> bool:
        """Determine if component is safe to use"""
        return (
            self.critical_issues == 0 and
            self.high_issues <= 2 and
            self.license_compatibility and
            len(self.license_issues) == 0
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "component_name": self.component_name,
            "security_level": self.security_level.value,
            "vulnerabilities_found": self.vulnerabilities_found,
            "critical_issues": self.critical_issues,
            "high_issues": self.high_issues,
            "risk_score": self.get_risk_score(),
            "is_safe_to_use": self.is_safe_to_use(),
            "license_compatibility": self.license_compatibility
        }
