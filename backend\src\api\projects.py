"""
Project management API endpoints.

Handles CRUD operations for CodeQuilter projects, including creation,
retrieval, updates, and session management.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..session_manager import <PERSON><PERSON>ana<PERSON>, get_session_manager
from ..state.project_state import ProjectState, ProjectStatus
from ..state.patterns import PATTERN_PALETTE, get_pattern


router = APIRouter(prefix="/api/projects", tags=["projects"])


# Request/Response Models
class CreateProjectRequest(BaseModel):
    """Request model for creating a new project"""
    project_brief: str = Field(..., description="Description of what the user wants to build")
    project_name: Optional[str] = Field(None, description="Optional project name")
    target_patterns: Optional[List[str]] = Field(default=[], description="Preferred architectural patterns")


class UpdateProjectRequest(BaseModel):
    """Request model for updating project details"""
    project_brief: Optional[str] = None
    project_name: Optional[str] = None
    target_patterns: Optional[List[str]] = None
    status: Optional[str] = None


class ProjectResponse(BaseModel):
    """Response model for project data"""
    session_id: str
    project_name: str
    project_brief: str
    status: str
    current_step: str
    progress: int
    target_patterns: List[str]
    selected_components: Dict[str, Any]
    created_at: str
    last_modified: str
    error_messages: List[str]
    
    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    """Response model for project list"""
    projects: List[Dict[str, Any]]
    total_count: int
    page: int
    per_page: int


# API Endpoints
@router.post("/", response_model=ProjectResponse)
async def create_project(
    request: CreateProjectRequest,
    session_manager: SessionManager = Depends(get_session_manager)
) -> ProjectResponse:
    """
    Create a new CodeQuilter project.
    
    This initializes a new project session with the user's requirements
    and sets up the initial state for the generation process.
    """
    try:
        # Create new project session
        project_state = session_manager.create_session(
            project_brief=request.project_brief,
            project_name=request.project_name or ""
        )
        
        # Set target patterns if provided
        if request.target_patterns:
            # Validate patterns exist
            valid_patterns = []
            for pattern_name in request.target_patterns:
                if pattern_name in PATTERN_PALETTE:
                    valid_patterns.append(pattern_name)
                else:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Unknown pattern: {pattern_name}"
                    )
            project_state.target_patterns = valid_patterns
        
        # Update status to brainstorming if we have a brief
        if request.project_brief:
            project_state.update_status(ProjectStatus.BRAINSTORMING, "project_created")
        
        # Save updated state
        session_manager.update_session(project_state.session_id, project_state)
        
        return ProjectResponse(**project_state.to_dict())
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create project: {str(e)}")


@router.get("/", response_model=ProjectListResponse)
async def list_projects(
    page: int = 1,
    per_page: int = 20,
    session_manager: SessionManager = Depends(get_session_manager)
) -> ProjectListResponse:
    """
    List all projects with pagination.
    
    Returns a paginated list of all projects in the system.
    """
    try:
        # Get all sessions
        all_sessions = session_manager.list_sessions(limit=1000)  # Get all for pagination
        
        # Calculate pagination
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paginated_sessions = all_sessions[start_idx:end_idx]
        
        return ProjectListResponse(
            projects=paginated_sessions,
            total_count=len(all_sessions),
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")


@router.get("/{session_id}", response_model=ProjectResponse)
async def get_project(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
) -> ProjectResponse:
    """
    Get a specific project by session ID.
    
    Returns the complete project state including all components,
    generated code, and current status.
    """
    try:
        project_state = session_manager.get_session(session_id)
        
        if not project_state:
            raise HTTPException(status_code=404, detail="Project not found")
        
        return ProjectResponse(**project_state.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get project: {str(e)}")


@router.put("/{session_id}", response_model=ProjectResponse)
async def update_project(
    session_id: str,
    request: UpdateProjectRequest,
    session_manager: SessionManager = Depends(get_session_manager)
) -> ProjectResponse:
    """
    Update a project's details.
    
    Allows updating project name, brief, target patterns, and status.
    """
    try:
        project_state = session_manager.get_session(session_id)
        
        if not project_state:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Update fields if provided
        if request.project_brief is not None:
            project_state.project_brief = request.project_brief
        
        if request.project_name is not None:
            project_state.project_name = request.project_name
        
        if request.target_patterns is not None:
            # Validate patterns
            valid_patterns = []
            for pattern_name in request.target_patterns:
                if pattern_name in PATTERN_PALETTE:
                    valid_patterns.append(pattern_name)
                else:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Unknown pattern: {pattern_name}"
                    )
            project_state.target_patterns = valid_patterns
        
        if request.status is not None:
            try:
                new_status = ProjectStatus(request.status)
                project_state.update_status(new_status, "manual_update")
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid status: {request.status}"
                )
        
        # Save updated state
        session_manager.update_session(session_id, project_state)
        
        return ProjectResponse(**project_state.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update project: {str(e)}")


@router.delete("/{session_id}")
async def delete_project(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
) -> Dict[str, str]:
    """
    Delete a project.
    
    Permanently removes the project and all associated data.
    """
    try:
        success = session_manager.delete_session(session_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Project not found")
        
        return {"message": "Project deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete project: {str(e)}")


@router.get("/{session_id}/patterns")
async def get_available_patterns() -> Dict[str, Any]:
    """
    Get all available architectural patterns.
    
    Returns the complete pattern palette with descriptions and use cases.
    """
    try:
        patterns_data = {}
        
        for pattern_name, pattern in PATTERN_PALETTE.items():
            patterns_data[pattern_name] = {
                "name": pattern.name,
                "description": pattern.description,
                "category": pattern.category.value,
                "complexity_level": pattern.complexity_level,
                "typical_use_cases": pattern.typical_use_cases,
                "dependencies": pattern.dependencies
            }
        
        return {
            "patterns": patterns_data,
            "total_count": len(PATTERN_PALETTE)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get patterns: {str(e)}")


@router.post("/{session_id}/clear-errors")
async def clear_project_errors(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
) -> Dict[str, str]:
    """
    Clear all error messages for a project.
    
    Resets the project status from ERROR to the previous state.
    """
    try:
        project_state = session_manager.get_session(session_id)
        
        if not project_state:
            raise HTTPException(status_code=404, detail="Project not found")
        
        project_state.clear_errors()
        session_manager.update_session(session_id, project_state)
        
        return {"message": "Errors cleared successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear errors: {str(e)}")


@router.get("/{session_id}/history")
async def get_project_history(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
) -> Dict[str, Any]:
    """
    Get the change history for a project.
    
    Returns all state changes for debugging and undo functionality.
    """
    try:
        project_state = session_manager.get_session(session_id)
        
        if not project_state:
            raise HTTPException(status_code=404, detail="Project not found")
        
        history = []
        for change in project_state.generation_history:
            history.append({
                "id": change.id,
                "timestamp": change.timestamp.isoformat(),
                "action": change.action,
                "description": change.description
            })
        
        return {
            "history": history,
            "total_changes": len(history),
            "can_undo": project_state.can_undo()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get project history: {str(e)}")
