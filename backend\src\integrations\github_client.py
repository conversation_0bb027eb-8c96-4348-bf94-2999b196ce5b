"""
GitHub API client for repository discovery and analysis.

TODO: REPLACE_MOCK - This is a mock implementation for development.
Replace with real GitHub API integration in Phase 3.
"""

import asyncio
from typing import List, Dict, Any
from datetime import datetime, timedelta
import random

from .base_client import GitHubClientInterface, APIResponse


class GitHubClient(GitHubClientInterface):
    """
    Mock GitHub API client for development and testing.
    
    TODO: REPLACE_MOCK - Replace with real GitHub API client
    """
    
    def __init__(self, api_key: str = "mock_key"):
        super().__init__(api_key=api_key, base_url="https://api.github.com")
        self._mock_repos = self._generate_mock_repositories()
    
    async def test_connection(self) -> APIResponse:
        """Test GitHub API connection"""
        # TODO: REPLACE_MOCK - Real API connection test
        await asyncio.sleep(0.1)  # Simulate network delay
        return self._format_response(True, {"status": "connected", "user": "mock_user"})
    
    async def search_repositories(self, query: str, per_page: int = 30) -> APIResponse:
        """Search for repositories matching the query"""
        # TODO: REPLACE_MOCK - Real GitHub search API
        await asyncio.sleep(0.2)  # Simulate API delay
        
        # Filter mock repos based on query keywords
        query_words = query.lower().split()
        matching_repos = []
        
        for repo in self._mock_repos:
            repo_text = f"{repo['name']} {repo['description']} {' '.join(repo['topics'])}".lower()
            if any(word in repo_text for word in query_words if word not in ['language:', 'stars:']):
                matching_repos.append(repo)
        
        # Limit results
        results = matching_repos[:per_page]
        
        return self._format_response(True, {
            "total_count": len(matching_repos),
            "items": results
        })
    
    async def get_repository_details(self, owner: str, repo: str) -> APIResponse:
        """Get detailed information about a specific repository"""
        # TODO: REPLACE_MOCK - Real GitHub repo details API
        await asyncio.sleep(0.1)
        
        # Find repo in mock data
        full_name = f"{owner}/{repo}"
        for mock_repo in self._mock_repos:
            if mock_repo["full_name"] == full_name:
                return self._format_response(True, mock_repo)
        
        return self._format_response(False, error="Repository not found", status_code=404)
    
    async def get_repository_files(self, owner: str, repo: str, path: str = "") -> APIResponse:
        """Get file listing for a repository"""
        # TODO: REPLACE_MOCK - Real GitHub contents API
        await asyncio.sleep(0.1)
        
        mock_files = [
            {"name": "README.md", "type": "file", "path": "README.md"},
            {"name": "setup.py", "type": "file", "path": "setup.py"},
            {"name": "requirements.txt", "type": "file", "path": "requirements.txt"},
            {"name": "src", "type": "dir", "path": "src"},
            {"name": "tests", "type": "dir", "path": "tests"},
            {"name": "main.py", "type": "file", "path": "src/main.py"},
        ]
        
        return self._format_response(True, mock_files)
    
    async def get_file_content(self, owner: str, repo: str, file_path: str) -> APIResponse:
        """Get content of a specific file"""
        # TODO: REPLACE_MOCK - Real GitHub file content API
        await asyncio.sleep(0.1)
        
        mock_content = {
            "README.md": "# Mock Repository\nThis is a mock repository for testing.",
            "setup.py": "from setuptools import setup\nsetup(name='mock-package')",
            "requirements.txt": "fastapi>=0.68.0\nuvicorn>=0.15.0",
            "src/main.py": "def main():\n    print('Hello, World!')\n\nif __name__ == '__main__':\n    main()"
        }
        
        content = mock_content.get(file_path, "# File content not available in mock")
        
        return self._format_response(True, {
            "content": content,
            "encoding": "utf-8",
            "size": len(content)
        })
    
    async def get_license_info(self, owner: str, repo: str) -> APIResponse:
        """Get license information for a repository"""
        # TODO: REPLACE_MOCK - Real GitHub license API
        await asyncio.sleep(0.1)
        
        licenses = ["MIT", "Apache-2.0", "BSD-3-Clause", "GPL-3.0", None]
        selected_license = random.choice(licenses)
        
        if selected_license:
            return self._format_response(True, {
                "license": {
                    "key": selected_license.lower(),
                    "name": selected_license,
                    "spdx_id": selected_license
                }
            })
        else:
            return self._format_response(False, error="No license found", status_code=404)
    
    def _generate_mock_repositories(self) -> List[Dict[str, Any]]:
        """Generate mock repository data for testing"""
        # TODO: REPLACE_MOCK - Remove when real API is implemented
        
        mock_repos = [
            {
                "name": "fastapi-gateway",
                "full_name": "example/fastapi-gateway",
                "description": "High-performance API gateway built with FastAPI",
                "html_url": "https://github.com/example/fastapi-gateway",
                "clone_url": "https://github.com/example/fastapi-gateway.git",
                "stargazers_count": 2500,
                "forks_count": 180,
                "watchers_count": 2500,
                "open_issues_count": 12,
                "language": "Python",
                "topics": ["api", "gateway", "fastapi", "microservices"],
                "created_at": "2022-01-15T10:30:00Z",
                "updated_at": "2024-12-01T15:45:00Z",
                "pushed_at": "2024-12-01T15:45:00Z",
                "license": {"key": "mit", "name": "MIT License"}
            },
            {
                "name": "express-router",
                "full_name": "example/express-router",
                "description": "Advanced routing middleware for Express.js applications",
                "html_url": "https://github.com/example/express-router",
                "clone_url": "https://github.com/example/express-router.git",
                "stargazers_count": 1800,
                "forks_count": 120,
                "watchers_count": 1800,
                "open_issues_count": 8,
                "language": "JavaScript",
                "topics": ["express", "router", "middleware", "nodejs"],
                "created_at": "2021-08-20T14:20:00Z",
                "updated_at": "2024-11-28T09:30:00Z",
                "pushed_at": "2024-11-28T09:30:00Z",
                "license": {"key": "apache-2.0", "name": "Apache License 2.0"}
            },
            {
                "name": "python-adapter",
                "full_name": "example/python-adapter",
                "description": "Flexible adapter pattern implementation for Python",
                "html_url": "https://github.com/example/python-adapter",
                "clone_url": "https://github.com/example/python-adapter.git",
                "stargazers_count": 1200,
                "forks_count": 95,
                "watchers_count": 1200,
                "open_issues_count": 5,
                "language": "Python",
                "topics": ["adapter", "pattern", "python", "design-patterns"],
                "created_at": "2022-03-10T11:15:00Z",
                "updated_at": "2024-11-30T16:20:00Z",
                "pushed_at": "2024-11-30T16:20:00Z",
                "license": {"key": "bsd-3-clause", "name": "BSD 3-Clause License"}
            },
            {
                "name": "redis-pubsub",
                "full_name": "example/redis-pubsub",
                "description": "Redis-based publish-subscribe messaging system",
                "html_url": "https://github.com/example/redis-pubsub",
                "clone_url": "https://github.com/example/redis-pubsub.git",
                "stargazers_count": 3200,
                "forks_count": 240,
                "watchers_count": 3200,
                "open_issues_count": 18,
                "language": "Python",
                "topics": ["redis", "pubsub", "messaging", "events"],
                "created_at": "2021-11-05T13:45:00Z",
                "updated_at": "2024-12-02T10:15:00Z",
                "pushed_at": "2024-12-02T10:15:00Z",
                "license": {"key": "mit", "name": "MIT License"}
            },
            {
                "name": "celery-queue",
                "full_name": "example/celery-queue",
                "description": "Advanced task queue management with Celery",
                "html_url": "https://github.com/example/celery-queue",
                "clone_url": "https://github.com/example/celery-queue.git",
                "stargazers_count": 2800,
                "forks_count": 200,
                "watchers_count": 2800,
                "open_issues_count": 15,
                "language": "Python",
                "topics": ["celery", "queue", "tasks", "workers"],
                "created_at": "2022-02-28T09:30:00Z",
                "updated_at": "2024-11-29T14:50:00Z",
                "pushed_at": "2024-11-29T14:50:00Z",
                "license": {"key": "apache-2.0", "name": "Apache License 2.0"}
            }
        ]
        
        return mock_repos


# Global instance for dependency injection
github_client = GitHubClient()
