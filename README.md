# CodeQuilter

An AI-native development environment that helps users build software projects by intelligently combining open-source components using architectural patterns.

## Project Structure

```
CodeQuilter/
├── backend/              # Python FastAPI server
│   ├── src/
│   │   ├── main.py      # FastAPI application entry point
│   │   ├── state/       # Core data models
│   │   ├── modules/     # Business logic modules
│   │   ├── integrations/# External service clients
│   │   └── api/         # API endpoints
│   ├── tests/           # Backend tests
│   ├── requirements.txt # Python dependencies
│   └── pyproject.toml   # Python project configuration
├── frontend/            # React/Vite frontend
│   ├── src/
│   │   ├── components/  # React components
│   │   ├── stores/      # State management
│   │   └── api/         # API client
│   ├── package.json     # Node dependencies
│   └── vite.config.ts   # Vite configuration
├── shared/              # Shared types and schemas
└── docs/               # Documentation
```

## Development Setup

### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### Frontend Setup
```bash
cd frontend
npm install
```

## Core Concepts

CodeQuilter uses a **pattern-driven approach** to software assembly:

1. **Architectural Patterns**: API Gateway, Adapter, Pub/Sub, Message Queue, REST
2. **Component Discovery**: Find quality open-source components using GitHub + Sourcegraph
3. **Intelligent Assembly**: Generate adapter code to connect components
4. **Quality Assurance**: Automated testing, security scanning, and code formatting

## Development Status

🚧 **Currently in development** - Phase 1: Foundation & Setup

See `CodeQuilter_technical_roadmap_v1.md` for detailed implementation plan.
