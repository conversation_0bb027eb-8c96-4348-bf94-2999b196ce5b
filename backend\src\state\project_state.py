"""
Core data models for CodeQuilter project state management.

The ProjectState class is the single source of truth for all project data,
including user intent, selected components, generated code, and verification results.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid


class ProjectStatus(Enum):
    """Current status of the project generation process"""
    INITIALIZING = "initializing"
    BRAINSTORMING = "brainstorming"
    ARCHITECTING = "architecting"
    PROCURING = "procuring"
    QUILTING = "quilting"
    VERIFYING = "verifying"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class StateChange:
    """Represents a single change to project state for undo/redo functionality"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    action: str = ""  # e.g., "component_selected", "code_generated"
    description: str = ""  # Human-readable description
    previous_state: Optional[Dict[str, Any]] = None
    new_state: Optional[Dict[str, Any]] = None


@dataclass
class ProjectState:
    """
    Central state object for a CodeQuilter project.
    
    This class represents the complete state of a project generation session,
    from initial user intent through final code generation and verification.
    All modules operate on this state object.
    """
    
    # Core Identity
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    last_modified: datetime = field(default_factory=datetime.now)
    status: ProjectStatus = ProjectStatus.INITIALIZING
    
    # User Intent & Planning (Phase: Brainstorming)
    project_brief: str = ""  # User's description of what they want to build
    project_name: str = ""   # Generated or user-provided project name
    target_patterns: List[str] = field(default_factory=list)  # Pattern names to use
    
    # Architecture & Component Discovery (Phase: Procurement)
    candidate_components: Dict[str, List[Dict[str, Any]]] = field(default_factory=dict)  # pattern -> repos
    selected_components: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # pattern -> chosen repo
    
    # Code Generation & Assembly (Phase: Quilting)
    generated_adapters: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # component_pair -> adapter
    project_structure: Dict[str, Any] = field(default_factory=dict)  # File tree structure
    
    # Quality & Verification (Phase: Verification)
    test_results: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # component -> results
    security_scan: Dict[str, Any] = field(default_factory=dict)  # Security analysis results
    
    # State Management
    generation_history: List[StateChange] = field(default_factory=list)
    current_step: str = "initialization"
    error_messages: List[str] = field(default_factory=list)
    
    def update_status(self, new_status: ProjectStatus, step: str = "") -> None:
        """Update project status and record the change"""
        old_status = self.status
        self.status = new_status
        self.last_modified = datetime.now()
        if step:
            self.current_step = step
            
        # Record state change
        change = StateChange(
            action="status_update",
            description=f"Status changed from {old_status.value} to {new_status.value}",
            previous_state={"status": old_status.value},
            new_state={"status": new_status.value}
        )
        self.generation_history.append(change)
    
    def add_error(self, error_message: str) -> None:
        """Add an error message and update status"""
        self.error_messages.append(error_message)
        self.update_status(ProjectStatus.ERROR)
    
    def clear_errors(self) -> None:
        """Clear all error messages"""
        self.error_messages.clear()
        if self.status == ProjectStatus.ERROR:
            # Revert to previous status if possible
            if self.generation_history:
                last_change = self.generation_history[-1]
                if last_change.previous_state and "status" in last_change.previous_state:
                    self.status = ProjectStatus(last_change.previous_state["status"])
    
    def select_component(self, pattern: str, component: Dict[str, Any]) -> None:
        """Select a component for a specific pattern"""
        self.selected_components[pattern] = component
        self.last_modified = datetime.now()
        
        change = StateChange(
            action="component_selected",
            description=f"Selected component for {pattern}: {component.get('name', 'unknown')}",
            new_state={"pattern": pattern, "component": component}
        )
        self.generation_history.append(change)
    
    def add_generated_code(self, component_pair: str, code_data: Dict[str, Any]) -> None:
        """Add generated adapter code"""
        self.generated_adapters[component_pair] = code_data
        self.last_modified = datetime.now()
        
        change = StateChange(
            action="code_generated",
            description=f"Generated adapter code for {component_pair}",
            new_state={"component_pair": component_pair, "code_data": code_data}
        )
        self.generation_history.append(change)
    
    def get_selected_component_names(self) -> List[str]:
        """Get list of selected component names"""
        return [comp.get("name", "unknown") for comp in self.selected_components.values()]
    
    def get_progress_percentage(self) -> int:
        """Calculate overall progress percentage"""
        status_weights = {
            ProjectStatus.INITIALIZING: 0,
            ProjectStatus.BRAINSTORMING: 15,
            ProjectStatus.ARCHITECTING: 30,
            ProjectStatus.PROCURING: 50,
            ProjectStatus.QUILTING: 75,
            ProjectStatus.VERIFYING: 90,
            ProjectStatus.COMPLETED: 100,
            ProjectStatus.ERROR: 0
        }
        return status_weights.get(self.status, 0)
    
    def can_undo(self) -> bool:
        """Check if undo operation is possible"""
        return len(self.generation_history) > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat(),
            "last_modified": self.last_modified.isoformat(),
            "status": self.status.value,
            "project_brief": self.project_brief,
            "project_name": self.project_name,
            "target_patterns": self.target_patterns,
            "selected_components": self.selected_components,
            "current_step": self.current_step,
            "progress": self.get_progress_percentage(),
            "error_messages": self.error_messages,
            "component_count": len(self.selected_components),
            "adapter_count": len(self.generated_adapters)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ProjectState":
        """Create ProjectState from dictionary"""
        # TODO: REPLACE_MOCK - Implement full deserialization when needed
        state = cls()
        state.session_id = data.get("session_id", state.session_id)
        state.project_brief = data.get("project_brief", "")
        state.project_name = data.get("project_name", "")
        state.status = ProjectStatus(data.get("status", "initializing"))
        return state
