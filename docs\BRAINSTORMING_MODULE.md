# Brainstorming Module Implementation Guide

## Overview
The Brainstorming Module is the intelligent conversation engine that transforms user intent into structured project requirements. It uses progressive questioning, pattern confidence scoring, and LLM-driven conversation management to gather the technical context needed for component discovery and code generation.

## Core Architecture

### Data Flow
```
User Input → Question Engine → LLM Context → Pattern Scoring → Next Questions → Structured Output
```

### Key Components
1. **Progressive Questionnaire Engine** - Determines which questions to ask based on previous answers
2. **Pattern Confidence Calculator** - Maps answers to architectural pattern scores
3. **LLM Conversation Manager** - Handles intelligent follow-up questions
4. **Decision Intelligence Engine** - Implements "CodeQuilter Decide" logic
5. **Structured Output Generator** - Converts conversation to YAML/JSON brief

## Implementation Requirements

### 1. Data Models (`backend/src/modules/brainstorming.py`)

```python
@dataclass
class QuestionnaireResponse:
    """User's answer to a specific question"""
    question_id: str
    question_text: str
    answer: str
    confidence: float = 1.0  # User's confidence in their answer
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class BrainstormingSession:
    """Complete brainstorming conversation state"""
    session_id: str
    responses: List[QuestionnaireResponse] = field(default_factory=list)
    pattern_scores: Dict[str, float] = field(default_factory=dict)
    next_questions: List[str] = field(default_factory=list)
    structured_brief: Dict[str, Any] = field(default_factory=dict)
    conversation_complete: bool = False
    llm_context: str = ""

@dataclass
class Question:
    """Individual question definition"""
    id: str
    text: str
    options: List[str] = field(default_factory=list)
    question_type: str = "multiple_choice"  # "multiple_choice", "text", "boolean"
    category: str = ""  # "identity", "runtime", "data", "communication", "preferences"
    triggers: List[str] = field(default_factory=list)  # Conditions that trigger this question
    skip_conditions: List[str] = field(default_factory=list)  # When to skip this question
```

### 2. Question Database (`backend/src/modules/questions.py`)

Define the structured questionnaire based on our agreed framework:

```python
QUESTION_CATEGORIES = {
    "project_identity": [
        Question(
            id="project_description",
            text="What is the one-sentence description of your project?",
            question_type="text",
            category="project_identity"
        ),
        Question(
            id="primary_features",
            text="What are the 3-5 primary features? (e.g., 'Users can log in', 'Send welcome emails')",
            question_type="text",
            category="project_identity"
        )
    ],
    "runtime_environment": [
        Question(
            id="deployment_target",
            text="Where will this application run?",
            options=[
                "Cloud Server / Virtual Machine",
                "Serverless Platform (AWS Lambda, etc.)",
                "Edge Computing Device (IoT, Raspberry Pi)",
                "User's Local Machine (CLI tool, desktop app)"
            ],
            category="runtime_environment"
        ),
        Question(
            id="expected_scale",
            text="What is the expected scale of concurrent users initially?",
            options=[
                "Low (1-100, personal/internal tool)",
                "Medium (100s-1000s, startup/small business)",
                "High (10,000+, requires significant scalability)"
            ],
            category="runtime_environment"
        )
    ],
    # ... continue for all categories
}
```

### 3. Progressive Question Engine (`backend/src/modules/question_engine.py`)

```python
class ProgressiveQuestionEngine:
    """Determines which questions to ask based on previous answers"""
    
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
        self.questions = load_question_database()
    
    async def get_next_questions(self, session: BrainstormingSession) -> List[Question]:
        """
        Determine next questions based on:
        1. Required questions not yet asked
        2. Conditional questions triggered by previous answers
        3. LLM-suggested follow-ups
        """
        
    def should_skip_question(self, question: Question, responses: List[QuestionnaireResponse]) -> bool:
        """Check if question should be skipped based on previous answers"""
        
    async def get_llm_follow_up_questions(self, session: BrainstormingSession) -> List[str]:
        """Ask LLM for intelligent follow-up questions"""
```

### 4. Pattern Confidence Calculator (`backend/src/modules/pattern_scoring.py`)

```python
class PatternConfidenceCalculator:
    """Maps questionnaire responses to architectural pattern confidence scores"""
    
    def calculate_pattern_scores(self, responses: List[QuestionnaireResponse]) -> Dict[str, float]:
        """
        Calculate confidence scores for each pattern based on responses.
        
        Scoring Rules:
        - deployment_target="serverless" → message_queue +0.8, api_gateway +0.9
        - async_tasks=True → pub_sub +0.7, message_queue +0.6
        - scale="high" → all async patterns +0.5
        - communication="api" → rest_api +0.9, api_gateway +0.7
        """
        
    def get_recommended_patterns(self, scores: Dict[str, float], threshold: float = 0.6) -> List[str]:
        """Get patterns above confidence threshold"""
```

### 5. LLM Conversation Manager (`backend/src/modules/conversation_manager.py`)

```python
class ConversationManager:
    """Manages LLM-driven intelligent conversation flow"""
    
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
    
    async def build_conversation_context(self, session: BrainstormingSession) -> str:
        """Build LLM context from current session state"""
        
    async def get_intelligent_follow_up(self, session: BrainstormingSession) -> Dict[str, Any]:
        """
        Ask LLM for intelligent follow-up based on current answers.
        
        Returns:
        {
            "next_questions": ["question_id1", "question_id2"],
            "confidence": 0.85,
            "reasoning": "User mentioned API, need data layer details",
            "assumptions": ["Using REST API pattern", "Needs database"],
            "clarifications_needed": ["Authentication method", "Database type"]
        }
        """
        
    async def generate_confident_assumptions(self, session: BrainstormingSession) -> List[Dict[str, Any]]:
        """
        Generate assumptions for 80%+ confidence decisions.
        
        Returns list of:
        {
            "assumption": "SQLAlchemy for ORM",
            "confidence": 0.85,
            "reasoning": "Most mature Python ORM, excellent PostgreSQL support",
            "alternatives": ["Tortoise ORM", "Manual SQL"]
        }
        """
```

### 6. Decision Intelligence Engine (`backend/src/modules/decision_engine.py`)

```python
class DecisionEngine:
    """Implements 'CodeQuilter Decide' logic with component availability"""
    
    def __init__(self, github_client: GitHubClient, procurement_module):
        self.github_client = github_client
        self.procurement_module = procurement_module
    
    async def make_intelligent_decision(self, 
                                      decision_type: str, 
                                      context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make data-driven decisions based on:
        1. License compatibility (hard filter)
        2. Project health score (40% stars, 40% activity, 20% community)
        3. Integration complexity (pattern-based heuristics)
        
        Returns:
        {
            "recommendation": "SQLAlchemy",
            "confidence": 0.87,
            "reasoning": "Highest health score, excellent docs",
            "alternatives": [{"name": "Tortoise", "score": 0.72}],
            "health_metrics": {"stars": 47000, "last_commit": "2024-12-01"}
        }
        """
```

### 7. API Endpoints (`backend/src/api/brainstorming.py`)

```python
@router.post("/{session_id}/brainstorm/start")
async def start_brainstorming(session_id: str, request: StartBrainstormingRequest):
    """Initialize brainstorming session with initial user input"""

@router.post("/{session_id}/brainstorm/answer")
async def submit_answer(session_id: str, request: AnswerRequest):
    """Submit answer and get next questions"""

@router.get("/{session_id}/brainstorm/status")
async def get_brainstorming_status(session_id: str):
    """Get current brainstorming session status and progress"""

@router.post("/{session_id}/brainstorm/complete")
async def complete_brainstorming(session_id: str):
    """Finalize brainstorming and generate structured brief"""
```

## Confidence Threshold Implementation

### Communication Strategy
- **80%+ confidence**: "Based on your requirements, I'm proceeding with SQLAlchemy..."
- **60-80%**: "I'm recommending Celery... Does that sound right?"
- **40-60%**: "Two strong options: FastAPI-Users vs manual implementation..."
- **<40%**: "I need more information to select a caching library..."

### LLM Prompt Templates
```python
CONFIDENCE_PROMPTS = {
    "high_confidence": """
    Based on the user's answers: {answers}
    I have {confidence}% confidence in recommending {recommendation}.
    Generate an explanation that includes:
    1. Why this is the best choice
    2. Key benefits for their use case
    3. Brief mention of what makes it superior
    """,
    
    "medium_confidence": """
    Based on the user's answers: {answers}
    I'm leaning toward {recommendation} but want confirmation.
    Generate a recommendation that:
    1. Explains why this seems like the best fit
    2. Asks for user confirmation
    3. Mentions they can see alternatives if needed
    """,
    
    "low_confidence": """
    Based on the user's answers: {answers}
    I have multiple good options: {options}
    Generate a comparison that:
    1. Presents 2-3 options with pros/cons
    2. Explains the trade-offs
    3. Asks what their priority is (ease vs control vs performance)
    """
}
```

## Testing Strategy

### Unit Tests
- Question selection logic
- Pattern confidence calculation
- Decision engine scoring
- LLM prompt generation

### Integration Tests
- Complete brainstorming flow
- Session state management
- API endpoint responses
- Error handling

### Mock LLM Responses
```python
MOCK_LLM_RESPONSES = {
    "follow_up_questions": {
        "blog_api": ["database_preference", "authentication_type"],
        "microservices": ["service_boundaries", "communication_patterns"]
    },
    "confident_assumptions": {
        "python_api": "SQLAlchemy for ORM - industry standard",
        "async_tasks": "Celery for background jobs - mature and reliable"
    }
}
```

## Integration Points

### With Existing Modules
1. **ProjectState**: Update with brainstorming results
2. **SessionManager**: Store brainstorming session data
3. **ProcurementModule**: Use structured brief for component search
4. **LLMClient**: Generate intelligent responses
5. **GitHubClient**: Check component availability for decisions

### Data Flow
```
User Input → Brainstorming Session → Structured Brief → Pattern Selection → Component Search
```

## Success Criteria

1. **Progressive Flow**: Questions adapt based on previous answers
2. **Intelligent Decisions**: 80%+ confidence assumptions with explanations
3. **Component Awareness**: Decisions based on actual available components
4. **Structured Output**: Clean YAML/JSON brief for downstream modules
5. **Testable**: Comprehensive test coverage with mock LLM responses

## Implementation Order

1. **Data Models** - Define all data structures
2. **Question Database** - Create structured questionnaire
3. **Pattern Scoring** - Implement confidence calculation
4. **Question Engine** - Progressive question selection
5. **LLM Integration** - Conversation management
6. **Decision Engine** - Component-aware recommendations
7. **API Endpoints** - REST interface
8. **Testing** - Comprehensive test suite

This module is the critical bridge between user intent and technical implementation. It must be intelligent, transparent, and reliable to build user trust in CodeQuilter's recommendations.
