["tests/test_api_projects.py::TestProjectCRUD::test_create_project_basic", "tests/test_api_projects.py::TestProjectCRUD::test_create_project_invalid_pattern", "tests/test_api_projects.py::TestProjectCRUD::test_create_project_with_patterns", "tests/test_api_projects.py::TestProjectCRUD::test_delete_project", "tests/test_api_projects.py::TestProjectCRUD::test_get_nonexistent_project", "tests/test_api_projects.py::TestProjectCRUD::test_get_project", "tests/test_api_projects.py::TestProjectCRUD::test_list_projects_empty", "tests/test_api_projects.py::TestProjectCRUD::test_list_projects_pagination", "tests/test_api_projects.py::TestProjectCRUD::test_list_projects_with_data", "tests/test_api_projects.py::TestProjectCRUD::test_update_project", "tests/test_api_projects.py::TestProjectCRUD::test_update_project_status", "tests/test_api_projects.py::TestProjectPatterns::test_get_available_patterns", "tests/test_api_projects.py::TestProjectUtilities::test_clear_project_errors", "tests/test_api_projects.py::TestProjectUtilities::test_get_project_history", "tests/test_main.py::test_health_check", "tests/test_main.py::test_mock_projects_endpoint", "tests/test_main.py::test_root_endpoint", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_categories", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_palette_completeness", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_search_query_generation", "tests/test_state_models.py::TestArchitecturalPatterns::test_pattern_suggestion", "tests/test_state_models.py::TestComponentTestResult::test_failing_tests", "tests/test_state_models.py::TestComponentTestResult::test_test_result_creation", "tests/test_state_models.py::TestGeneratedCode::test_code_creation", "tests/test_state_models.py::TestGeneratedCode::test_metrics_calculation", "tests/test_state_models.py::TestGitHubRepo::test_activity_check", "tests/test_state_models.py::TestGitHubRepo::test_quality_score_calculation", "tests/test_state_models.py::TestGitHubRepo::test_repo_creation", "tests/test_state_models.py::TestGitHubRepo::test_repo_serialization", "tests/test_state_models.py::TestProjectState::test_component_selection", "tests/test_state_models.py::TestProjectState::test_error_handling", "tests/test_state_models.py::TestProjectState::test_progress_calculation", "tests/test_state_models.py::TestProjectState::test_project_state_creation", "tests/test_state_models.py::TestProjectState::test_serialization", "tests/test_state_models.py::TestProjectState::test_status_update", "tests/test_state_models.py::TestSecurityReport::test_risk_calculation", "tests/test_state_models.py::TestSecurityReport::test_safety_assessment", "tests/test_state_models.py::TestSecurityReport::test_security_report_creation", "tests/test_state_models.py::TestTestResult::test_failing_tests", "tests/test_state_models.py::TestTestResult::test_test_result_creation"]