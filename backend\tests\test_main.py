"""
Test suite for the main FastAPI application
"""

import pytest
from fastapi.testclient import TestClient
from src.main import app

client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint returns correct response"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "CodeQuilter Engine is running"
    assert data["version"] == "0.1.0"
    assert data["status"] == "healthy"


def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/api/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "codequilter-engine"
    assert data["version"] == "0.1.0"


def test_projects_endpoint():
    """Test the projects endpoint"""
    response = client.get("/api/projects/")
    assert response.status_code == 200
    data = response.json()
    assert "projects" in data
    assert isinstance(data["projects"], list)
    assert "total_count" in data
    assert "page" in data
    assert "per_page" in data
