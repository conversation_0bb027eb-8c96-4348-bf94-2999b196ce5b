"""
Test suite for project API endpoints.

Tests all CRUD operations and project management functionality.
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from backend.src.main import app
from backend.src.session_manager import session_manager

client = TestClient(app)


@pytest.fixture(autouse=True)
async def setup_and_cleanup():
    """Setup and cleanup for each test"""
    # Start session manager
    await session_manager.start()
    
    # Clear any existing sessions
    for session_id in list(session_manager._active_sessions.keys()):
        session_manager.delete_session(session_id)
    
    yield
    
    # Cleanup after test
    for session_id in list(session_manager._active_sessions.keys()):
        session_manager.delete_session(session_id)
    
    await session_manager.stop()


class TestProjectCRUD:
    """Test project CRUD operations"""
    
    def test_create_project_basic(self):
        """Test basic project creation"""
        response = client.post("/api/projects/", json={
            "project_brief": "Build a REST API for a blog",
            "project_name": "blog-api"
        })
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["project_name"] == "blog-api"
        assert data["project_brief"] == "Build a REST API for a blog"
        assert data["status"] == "brainstorming"
        assert data["progress"] == 15  # Brainstorming status
        assert "session_id" in data
    
    def test_create_project_with_patterns(self):
        """Test project creation with target patterns"""
        response = client.post("/api/projects/", json={
            "project_brief": "Build a microservices gateway",
            "target_patterns": ["api_gateway", "rest_api"]
        })
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["target_patterns"] == ["api_gateway", "rest_api"]
        assert data["status"] == "brainstorming"
    
    def test_create_project_invalid_pattern(self):
        """Test project creation with invalid pattern"""
        response = client.post("/api/projects/", json={
            "project_brief": "Test project",
            "target_patterns": ["invalid_pattern"]
        })
        
        assert response.status_code == 400
        assert "Unknown pattern" in response.json()["detail"]
    
    def test_get_project(self):
        """Test retrieving a specific project"""
        # Create a project first
        create_response = client.post("/api/projects/", json={
            "project_brief": "Test project",
            "project_name": "test-project"
        })
        session_id = create_response.json()["session_id"]
        
        # Get the project
        response = client.get(f"/api/projects/{session_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["session_id"] == session_id
        assert data["project_name"] == "test-project"
        assert data["project_brief"] == "Test project"
    
    def test_get_nonexistent_project(self):
        """Test retrieving a non-existent project"""
        response = client.get("/api/projects/nonexistent-id")
        
        assert response.status_code == 404
        assert "Project not found" in response.json()["detail"]
    
    def test_update_project(self):
        """Test updating project details"""
        # Create a project first
        create_response = client.post("/api/projects/", json={
            "project_brief": "Original brief",
            "project_name": "original-name"
        })
        session_id = create_response.json()["session_id"]
        
        # Update the project
        response = client.put(f"/api/projects/{session_id}", json={
            "project_brief": "Updated brief",
            "project_name": "updated-name",
            "target_patterns": ["adapter"]
        })
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["project_brief"] == "Updated brief"
        assert data["project_name"] == "updated-name"
        assert data["target_patterns"] == ["adapter"]
    
    def test_update_project_status(self):
        """Test updating project status"""
        # Create a project first
        create_response = client.post("/api/projects/", json={
            "project_brief": "Test project"
        })
        session_id = create_response.json()["session_id"]
        
        # Update status
        response = client.put(f"/api/projects/{session_id}", json={
            "status": "procuring"
        })
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "procuring"
        assert data["progress"] == 50  # Procuring status
    
    def test_delete_project(self):
        """Test deleting a project"""
        # Create a project first
        create_response = client.post("/api/projects/", json={
            "project_brief": "Test project"
        })
        session_id = create_response.json()["session_id"]
        
        # Delete the project
        response = client.delete(f"/api/projects/{session_id}")
        
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"]
        
        # Verify it's gone
        get_response = client.get(f"/api/projects/{session_id}")
        assert get_response.status_code == 404
    
    def test_list_projects_empty(self):
        """Test listing projects when none exist"""
        response = client.get("/api/projects/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["projects"] == []
        assert data["total_count"] == 0
        assert data["page"] == 1
        assert data["per_page"] == 20
    
    def test_list_projects_with_data(self):
        """Test listing projects with data"""
        # Create multiple projects
        for i in range(3):
            client.post("/api/projects/", json={
                "project_brief": f"Test project {i}",
                "project_name": f"project-{i}"
            })
        
        response = client.get("/api/projects/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["projects"]) == 3
        assert data["total_count"] == 3
        
        # Check project data
        project_names = [p["project_name"] for p in data["projects"]]
        assert "project-0" in project_names
        assert "project-1" in project_names
        assert "project-2" in project_names
    
    def test_list_projects_pagination(self):
        """Test project list pagination"""
        # Create multiple projects
        for i in range(5):
            client.post("/api/projects/", json={
                "project_brief": f"Test project {i}"
            })
        
        # Get first page
        response = client.get("/api/projects/?page=1&per_page=2")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["projects"]) == 2
        assert data["total_count"] == 5
        assert data["page"] == 1
        assert data["per_page"] == 2


class TestProjectPatterns:
    """Test pattern-related endpoints"""
    
    def test_get_available_patterns(self):
        """Test getting available patterns"""
        response = client.get("/api/projects/test-id/patterns")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "patterns" in data
        assert "total_count" in data
        assert data["total_count"] > 0
        
        # Check specific patterns exist
        patterns = data["patterns"]
        assert "api_gateway" in patterns
        assert "adapter" in patterns
        assert "rest_api" in patterns
        
        # Check pattern structure
        api_gateway = patterns["api_gateway"]
        assert "name" in api_gateway
        assert "description" in api_gateway
        assert "category" in api_gateway
        assert "typical_use_cases" in api_gateway


class TestProjectUtilities:
    """Test utility endpoints"""
    
    def test_clear_project_errors(self):
        """Test clearing project errors"""
        # Create a project
        create_response = client.post("/api/projects/", json={
            "project_brief": "Test project"
        })
        session_id = create_response.json()["session_id"]
        
        # Clear errors (even if none exist)
        response = client.post(f"/api/projects/{session_id}/clear-errors")
        
        assert response.status_code == 200
        assert "cleared successfully" in response.json()["message"]
    
    def test_get_project_history(self):
        """Test getting project history"""
        # Create a project
        create_response = client.post("/api/projects/", json={
            "project_brief": "Test project"
        })
        session_id = create_response.json()["session_id"]
        
        # Update it to create history
        client.put(f"/api/projects/{session_id}", json={
            "project_name": "updated-name"
        })
        
        # Get history
        response = client.get(f"/api/projects/{session_id}/history")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "history" in data
        assert "total_changes" in data
        assert "can_undo" in data
        
        # Should have at least status update from creation
        assert data["total_changes"] > 0
        assert len(data["history"]) > 0
        
        # Check history entry structure
        history_entry = data["history"][0]
        assert "id" in history_entry
        assert "timestamp" in history_entry
        assert "action" in history_entry
        assert "description" in history_entry
