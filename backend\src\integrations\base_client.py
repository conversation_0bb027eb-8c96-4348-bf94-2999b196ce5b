"""
Base classes for external service integrations.

Defines abstract interfaces that all integration clients must implement.
This ensures consistent behavior and makes testing easier.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class APIResponse:
    """Standard response format for all API calls"""
    success: bool
    data: Any = None
    error_message: str = ""
    status_code: int = 200
    rate_limit_remaining: Optional[int] = None


class BaseAPIClient(ABC):
    """
    Abstract base class for all external API clients.
    
    Provides common functionality like rate limiting, error handling,
    and response formatting.
    """
    
    def __init__(self, api_key: str = "", base_url: str = ""):
        self.api_key = api_key
        self.base_url = base_url
        self._rate_limit_remaining = None
        self._last_request_time = None
    
    @abstractmethod
    async def test_connection(self) -> APIResponse:
        """Test if the API connection is working"""
        pass
    
    def get_rate_limit_status(self) -> Optional[int]:
        """Get remaining API calls for rate limiting"""
        return self._rate_limit_remaining
    
    def _format_response(self, success: bool, data: Any = None, 
                        error: str = "", status_code: int = 200) -> APIResponse:
        """Format a standard API response"""
        return APIResponse(
            success=success,
            data=data,
            error_message=error,
            status_code=status_code,
            rate_limit_remaining=self._rate_limit_remaining
        )


class GitHubClientInterface(BaseAPIClient):
    """Interface for GitHub API operations"""
    
    @abstractmethod
    async def search_repositories(self, query: str, per_page: int = 30) -> APIResponse:
        """Search for repositories matching the query"""
        pass
    
    @abstractmethod
    async def get_repository_details(self, owner: str, repo: str) -> APIResponse:
        """Get detailed information about a specific repository"""
        pass
    
    @abstractmethod
    async def get_repository_files(self, owner: str, repo: str, path: str = "") -> APIResponse:
        """Get file listing for a repository"""
        pass
    
    @abstractmethod
    async def get_file_content(self, owner: str, repo: str, file_path: str) -> APIResponse:
        """Get content of a specific file"""
        pass
    
    @abstractmethod
    async def get_license_info(self, owner: str, repo: str) -> APIResponse:
        """Get license information for a repository"""
        pass


class LLMClientInterface(BaseAPIClient):
    """Interface for Large Language Model operations"""
    
    @abstractmethod
    async def generate_code(self, prompt: str, max_tokens: int = 1000) -> APIResponse:
        """Generate code based on a prompt"""
        pass
    
    @abstractmethod
    async def analyze_code(self, code: str, analysis_type: str = "quality") -> APIResponse:
        """Analyze code for quality, security, or other metrics"""
        pass
    
    @abstractmethod
    async def generate_documentation(self, code: str) -> APIResponse:
        """Generate documentation for code"""
        pass
    
    @abstractmethod
    async def suggest_improvements(self, code: str) -> APIResponse:
        """Suggest improvements for code"""
        pass


class SourcegraphClientInterface(BaseAPIClient):
    """Interface for Sourcegraph code search operations"""
    
    @abstractmethod
    async def search_code(self, query: str, repo_filter: str = "") -> APIResponse:
        """Search for code patterns using regex"""
        pass
    
    @abstractmethod
    async def find_function_definitions(self, function_name: str, language: str = "") -> APIResponse:
        """Find function definitions across repositories"""
        pass
    
    @abstractmethod
    async def find_interface_implementations(self, interface_pattern: str) -> APIResponse:
        """Find implementations of specific interfaces or patterns"""
        pass


class DockerClientInterface(BaseAPIClient):
    """Interface for Docker operations (testing in containers)"""
    
    @abstractmethod
    async def run_tests_in_container(self, repo_url: str, test_commands: List[str]) -> APIResponse:
        """Run tests for a repository in an isolated container"""
        pass
    
    @abstractmethod
    async def build_project(self, repo_url: str, build_commands: List[str]) -> APIResponse:
        """Build a project in a container"""
        pass
    
    @abstractmethod
    async def check_dependencies(self, repo_url: str) -> APIResponse:
        """Check and analyze project dependencies"""
        pass


class SecurityScannerInterface(BaseAPIClient):
    """Interface for security scanning operations"""
    
    @abstractmethod
    async def scan_dependencies(self, dependencies: List[str]) -> APIResponse:
        """Scan dependencies for known vulnerabilities"""
        pass
    
    @abstractmethod
    async def scan_code(self, code: str, language: str = "python") -> APIResponse:
        """Scan code for security vulnerabilities"""
        pass
    
    @abstractmethod
    async def check_license_compatibility(self, licenses: List[str]) -> APIResponse:
        """Check if licenses are compatible with each other"""
        pass


class VectorDBClientInterface(BaseAPIClient):
    """Interface for vector database operations (for RAG)"""
    
    @abstractmethod
    async def store_embeddings(self, texts: List[str], metadata: List[Dict[str, Any]]) -> APIResponse:
        """Store text embeddings with metadata"""
        pass
    
    @abstractmethod
    async def search_similar(self, query_text: str, limit: int = 10) -> APIResponse:
        """Search for similar texts based on embeddings"""
        pass
    
    @abstractmethod
    async def delete_embeddings(self, filter_criteria: Dict[str, Any]) -> APIResponse:
        """Delete embeddings matching criteria"""
        pass
