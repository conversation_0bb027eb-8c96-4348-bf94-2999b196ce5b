"""
Test suite for state management data models.

Tests ProjectState, ArchitecturalPattern, GitHubRepo, GeneratedCode,
TestResult, and SecurityReport classes.
"""

import pytest
from datetime import datetime, timedelta
from backend.src.state.project_state import ProjectState, ProjectStatus, StateChange
from backend.src.state.patterns import (
    ArchitecturalPattern, PatternCategory, SearchCriteria, 
    PATTERN_PALETTE, get_pattern, suggest_patterns_for_use_case
)
from backend.src.state.components import (
    GitHubRepo, GeneratedCode, ComponentTestResult, SecurityReport,
    ComponentStatus, ComponentTestStatus, SecurityLevel
)


class TestProjectState:
    """Test ProjectState functionality"""
    
    def test_project_state_creation(self):
        """Test basic ProjectState creation"""
        state = ProjectState()
        assert state.session_id is not None
        assert state.status == ProjectStatus.INITIALIZING
        assert state.project_brief == ""
        assert len(state.target_patterns) == 0
        assert len(state.generation_history) == 0
    
    def test_status_update(self):
        """Test status update with history tracking"""
        state = ProjectState()
        old_status = state.status
        
        state.update_status(ProjectStatus.BRAINSTORMING, "user_input")
        
        assert state.status == ProjectStatus.BRAINSTORMING
        assert state.current_step == "user_input"
        assert len(state.generation_history) == 1
        
        change = state.generation_history[0]
        assert change.action == "status_update"
        assert change.previous_state["status"] == old_status.value
        assert change.new_state["status"] == ProjectStatus.BRAINSTORMING.value
    
    def test_component_selection(self):
        """Test component selection tracking"""
        state = ProjectState()
        component = {"name": "test-component", "url": "https://github.com/test/repo"}
        
        state.select_component("api_gateway", component)
        
        assert "api_gateway" in state.selected_components
        assert state.selected_components["api_gateway"] == component
        assert len(state.generation_history) == 1
        assert state.generation_history[0].action == "component_selected"
    
    def test_error_handling(self):
        """Test error handling and recovery"""
        state = ProjectState()
        state.update_status(ProjectStatus.BRAINSTORMING)
        
        state.add_error("Test error message")
        
        assert state.status == ProjectStatus.ERROR
        assert "Test error message" in state.error_messages
        
        state.clear_errors()
        assert len(state.error_messages) == 0
        assert state.status == ProjectStatus.BRAINSTORMING  # Reverted
    
    def test_progress_calculation(self):
        """Test progress percentage calculation"""
        state = ProjectState()
        
        assert state.get_progress_percentage() == 0  # INITIALIZING
        
        state.update_status(ProjectStatus.PROCURING)
        assert state.get_progress_percentage() == 50
        
        state.update_status(ProjectStatus.COMPLETED)
        assert state.get_progress_percentage() == 100
    
    def test_serialization(self):
        """Test to_dict and from_dict methods"""
        state = ProjectState()
        state.project_brief = "Test project"
        state.project_name = "test-app"
        state.update_status(ProjectStatus.BRAINSTORMING)
        
        data = state.to_dict()
        
        assert data["project_brief"] == "Test project"
        assert data["project_name"] == "test-app"
        assert data["status"] == "brainstorming"
        assert data["progress"] == 15
        
        # Test deserialization
        new_state = ProjectState.from_dict(data)
        assert new_state.project_brief == "Test project"
        assert new_state.status == ProjectStatus.BRAINSTORMING


class TestArchitecturalPatterns:
    """Test architectural pattern functionality"""
    
    def test_pattern_palette_completeness(self):
        """Test that all expected patterns are defined"""
        expected_patterns = ["api_gateway", "adapter", "pub_sub", "message_queue", "rest_api"]
        
        for pattern_name in expected_patterns:
            assert pattern_name in PATTERN_PALETTE
            pattern = PATTERN_PALETTE[pattern_name]
            assert isinstance(pattern, ArchitecturalPattern)
            assert pattern.name is not None
            assert pattern.description is not None
    
    def test_pattern_search_query_generation(self):
        """Test search query generation for patterns"""
        pattern = get_pattern("api_gateway")
        assert pattern is not None
        
        query = pattern.get_search_query("python")
        assert "language:python" in query
        assert "stars:>1000" in query
        assert "fastapi" in query or "flask" in query
    
    def test_pattern_suggestion(self):
        """Test pattern suggestion based on use case"""
        suggestions = suggest_patterns_for_use_case("microservices entry point")
        
        # Should suggest API Gateway pattern
        pattern_names = [p.name for p in suggestions]
        assert "API Gateway" in pattern_names
    
    def test_pattern_categories(self):
        """Test pattern categorization"""
        api_gateway = get_pattern("api_gateway")
        assert api_gateway.category == PatternCategory.COMMUNICATION
        
        adapter = get_pattern("adapter")
        assert adapter.category == PatternCategory.INTEGRATION


class TestGitHubRepo:
    """Test GitHubRepo functionality"""
    
    def test_repo_creation(self):
        """Test basic repo creation"""
        repo = GitHubRepo(
            name="test-repo",
            full_name="owner/test-repo",
            description="A test repository",
            stars=1500,
            language="Python"
        )
        
        assert repo.name == "test-repo"
        assert repo.stars == 1500
        assert repo.status == ComponentStatus.DISCOVERED
    
    def test_quality_score_calculation(self):
        """Test quality score calculation"""
        # High quality repo
        good_repo = GitHubRepo(
            name="good-repo",
            full_name="owner/good-repo",
            stars=5000,
            open_issues=10,
            closed_issues=100,
            license="MIT",
            pushed_at=datetime.now() - timedelta(days=30)
        )
        
        score = good_repo.get_quality_score()
        assert score > 0.5  # Should be high quality
        
        # Low quality repo
        bad_repo = GitHubRepo(
            name="bad-repo",
            full_name="owner/bad-repo",
            stars=50,
            open_issues=100,
            closed_issues=10,
            license=None,
            pushed_at=datetime.now() - timedelta(days=800)
        )
        
        bad_score = bad_repo.get_quality_score()
        assert bad_score < score  # Should be lower quality
    
    def test_activity_check(self):
        """Test recent activity checking"""
        recent_repo = GitHubRepo(
            name="recent",
            full_name="owner/recent",
            pushed_at=datetime.now() - timedelta(days=30)
        )
        assert recent_repo.is_recently_active()
        
        old_repo = GitHubRepo(
            name="old",
            full_name="owner/old",
            pushed_at=datetime.now() - timedelta(days=400)
        )
        assert not old_repo.is_recently_active()
    
    def test_repo_serialization(self):
        """Test repo to_dict method"""
        repo = GitHubRepo(
            name="test-repo",
            full_name="owner/test-repo",
            stars=1000,
            language="Python",
            pattern_match="api_gateway",
            confidence_score=0.85
        )
        
        data = repo.to_dict()
        assert data["name"] == "test-repo"
        assert data["stars"] == 1000
        assert data["pattern_match"] == "api_gateway"
        assert data["confidence_score"] == 0.85


class TestGeneratedCode:
    """Test GeneratedCode functionality"""
    
    def test_code_creation(self):
        """Test generated code creation"""
        code = GeneratedCode(
            source_component="component-a",
            target_component="component-b",
            pattern_type="adapter",
            code="def adapter_function(): pass",
            file_path="adapters/a_to_b.py"
        )
        
        assert code.source_component == "component-a"
        assert code.pattern_type == "adapter"
        assert code.id is not None
    
    def test_metrics_calculation(self):
        """Test code metrics calculation"""
        code = GeneratedCode(
            code="""
def function_one():
    pass

def function_two():
    return True

# Comment line
""",
            language="python"
        )
        
        code.calculate_metrics()
        assert code.lines_of_code == 5  # Non-empty lines (excluding empty lines)
        assert code.complexity_score > 0


class TestComponentTestResult:
    """Test ComponentTestResult functionality"""

    def test_test_result_creation(self):
        """Test test result creation"""
        result = ComponentTestResult(
            component_name="test-component",
            test_type="unit",
            status=ComponentTestStatus.PASSED,
            passed=10,
            failed=0,
            duration_seconds=2.5
        )
        
        assert result.component_name == "test-component"
        assert result.is_passing()
        assert result.get_success_rate() == 1.0
    
    def test_failing_tests(self):
        """Test failing test scenarios"""
        result = ComponentTestResult(
            status=ComponentTestStatus.FAILED,
            passed=5,
            failed=3,
            error_message="Some tests failed"
        )
        
        assert not result.is_passing()
        assert result.get_success_rate() == 5/8  # 5 passed out of 8 total


class TestSecurityReport:
    """Test SecurityReport functionality"""
    
    def test_security_report_creation(self):
        """Test security report creation"""
        report = SecurityReport(
            component_name="test-component",
            security_level=SecurityLevel.LOW_RISK,
            critical_issues=0,
            high_issues=1,
            medium_issues=2,
            low_issues=3
        )
        
        assert report.component_name == "test-component"
        assert report.vulnerabilities_found == 0  # Default
    
    def test_risk_calculation(self):
        """Test risk score calculation"""
        high_risk = SecurityReport(
            critical_issues=2,
            high_issues=3,
            medium_issues=1,
            low_issues=0
        )
        
        low_risk = SecurityReport(
            critical_issues=0,
            high_issues=0,
            medium_issues=1,
            low_issues=2
        )
        
        assert high_risk.get_risk_score() > low_risk.get_risk_score()
    
    def test_safety_assessment(self):
        """Test safety assessment"""
        safe_report = SecurityReport(
            critical_issues=0,
            high_issues=1,
            license_compatibility=True
        )
        assert safe_report.is_safe_to_use()
        
        unsafe_report = SecurityReport(
            critical_issues=1,
            high_issues=0,
            license_compatibility=True
        )
        assert not unsafe_report.is_safe_to_use()
